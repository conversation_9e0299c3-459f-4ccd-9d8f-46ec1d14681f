// pages/upload/upload.js
// 从全局获取ApiService和SCF请求服务，兼容微信小程序
const ApiService = global.ApiService || getApp().ApiService
const scfRequest = global.scfRequest || getApp().scfRequest

Page({

  /**
   * 页面的初始数据
   */
  data: {
    // 轮播图数据
    banners: [
      {
        id: 1,
        title: 'AI智能解析',
        description: '上传简历，自动提取能力积木',
        icon: '🤖',
        background: 'linear-gradient(135deg, #42a5f5 0%, #2196f3 100%)'
      },
      {
        id: 2,
        title: '积木式组合',
        description: '灵活组合，定制专属简历',
        icon: '🧱',
        background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)'
      },
      {
        id: 3,
        title: '一键生成',
        description: '匹配岗位，生成完美简历',
        icon: '✨',
        background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)'
      }
    ],

    // 上传状态
    isUploading: false,
    uploadProgress: 0,

    // 文件信息
    selectedFile: null,
    fileName: '',
    fileSize: 0,
    fileSizeText: '', // 文件大小文本显示

    // 步骤状态
    currentStep: 1, // 1: 选择文件, 2: 上传中, 3: 解析中, 4: 完成
    progressPercent: 0,

    // 简历解析结果
    parseResult: null,
    uploadedBricks: [], // 解析出的积木
    userName: '用户',

    // 任务管理 - 异步任务处理
    taskId: null,
    taskStatus: 'idle', // idle-空闲, processing-处理中, completed-已完成, failed-失败
    taskProgress: 0,
    taskMessage: '',
    estimatedTime: '',

    // 轮询控制
    pollingTimer: null,
    pollingCount: 0,
    maxPollingAttempts: 100, // 最多轮询100次（约5分钟）

    // 分析状态
    analysisStatus: 'idle' // idle-空闲 analyzing-分析中 completed-已完成 failed-失败
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('📱 简历上传页面加载')
    // 不要在onLoad中调用setData，推迟到onReady
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    console.log('📱 页面渲染完成，准备初始化')
    // 暂时禁用初始化，避免任何可能的API调用
    // setTimeout(() => {
    //   this.initUploadPage()
    // }, 100) // 延迟100ms
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    console.log('上传页面显示')
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    // 清除轮询定时器
    this.clearPollingTimer()
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '简历上传解析',
      path: '/pages/upload/upload'
    }
  },

  // 初始化上传页面
  initUploadPage() {
    try {
      console.log('🎯 初始化上传页面')
      this.setData({
        currentStep: 1,
        progressPercent: 0,
        analysisStatus: 'idle'
      })
    } catch (error) {
      console.error('❌ 初始化页面失败:', error)
      // 静默处理，不影响页面显示
    }
  },

  // 选择文件
  async chooseFile() {
    try {
      console.log('📁 开始选择文件')

      const res = await wx.chooseMessageFile({
        count: 1,
        type: 'file',
        extension: ['pdf', 'txt', 'doc', 'docx']
      })

      if (res.tempFiles && res.tempFiles.length > 0) {
        const file = res.tempFiles[0]
        console.log('✅ 文件选择成功:', file.name, '大小:', Math.round(file.size / 1024), 'KB')

        this.setData({
          selectedFile: file,
          fileName: file.name,
          fileSize: file.size,
          fileSizeText: this.formatFileSize(file.size),
          currentStep: 1
        })

        // 显示文件信息
        this.showFileInfo(file)
      }
    } catch (error) {
      console.error('❌ 选择文件失败:', error)
      wx.showToast({
        title: '选择文件失败',
        icon: 'error'
      })
    }
  },

  // 显示文件信息
  showFileInfo(file) {
    const sizeKB = Math.round(file.size / 1024)
    const sizeText = sizeKB > 1024 ? `${(sizeKB / 1024).toFixed(1)}MB` : `${sizeKB}KB`

    wx.showToast({
      title: `已选择: ${file.name} (${sizeText})`,
      icon: 'success',
      duration: 2000
    })
  },

  // 开始上传并解析 - 异步任务模式
  async startUploadAndParse() {
    if (!this.data.selectedFile) {
      wx.showToast({
        title: '请先选择简历文件',
        icon: 'error'
      })
      return
    }

    try {
      console.log('🚀 开始异步上传和解析流程')

      // 第一步：上传文件到云存储
      this.setData({
        isUploading: true,
        currentStep: 2,
        progressPercent: 10,
        taskMessage: '正在上传文件...'
      })

      const fileID = await this.uploadFileToCloud()

      // 第二步：创建解析任务
      this.setData({
        progressPercent: 30,
        taskMessage: '创建AI解析任务...'
      })

      const taskResult = await this.createParseTask(fileID)

      if (taskResult.success) {
        // 检查任务状态 - 修复数据结构问题
        // 云函数返回的是 queued 状态，需要开始轮询
        console.log('✅ 任务创建成功，任务ID:', taskResult.taskId)

        // 任务已创建，开始轮询
        this.setData({
          taskId: taskResult.taskId,
          taskStatus: 'processing',
          currentStep: 3,
          progressPercent: 50,
          taskMessage: '⏳ AI正在分析简历，预计3-5分钟...',
          estimatedTime: taskResult.estimatedTime,
          isUploading: false
        })

        console.log('✅ 开始轮询，任务ID:', taskResult.taskId)

        // 显示处理选择对话框
        this.showProcessingChoice()

        // 开始轮询任务状态
        this.startPollingTaskStatus()

      } else {
        throw new Error(taskResult.error || '创建任务失败')
      }

    } catch (error) {
      console.error('❌ 上传解析流程失败:', error)

      this.setData({
        isUploading: false,
        taskStatus: 'failed',
        currentStep: 1,
        progressPercent: 0,
        taskMessage: ''
      })

      wx.showModal({
        title: '处理失败',
        content: error.message || '上传或解析失败，请重试',
        showCancel: false,
        confirmText: '知道了'
      })
    }
  },

  // 上传文件到云存储
  async uploadFileToCloud() {
    try {
      const cloudPath = `resumes/${Date.now()}-${this.data.fileName}`

      const uploadResult = await wx.cloud.uploadFile({
        cloudPath: cloudPath,
        filePath: this.data.selectedFile.path
      })

      console.log('✅ 文件上传成功:', uploadResult.fileID)
      return uploadResult.fileID

    } catch (error) {
      console.error('❌ 文件上传失败:', error)
      throw new Error('文件上传失败，请检查网络连接')
    }
  },

  // 创建解析任务 - 调用新的网关函数
  async createParseTask(fileID) {
    try {
      console.log('📝 创建解析任务，文件ID:', fileID)

      // 检查是否使用模拟数据模式
      if (getApp().globalData.useMockData) {
        console.log('🔧 使用模拟数据模式')
        return {
          success: true,
          taskId: 'MOCK_TASK_' + Date.now(),
          totalSlices: 5,
          estimatedTime: 60,
          duration: 2.5
        }
      }

      const response = await scfRequest.resumeGateway(
        fileID,
        this.data.fileName,
        getApp().globalData.userInfo || {}
      )

      console.log('📋 任务创建响应:', response)
      return response

    } catch (error) {
      console.error('❌ 创建解析任务失败:', error)

      // 如果是云函数不存在或依赖问题，自动切换到模拟模式
      if (error.message.includes('FunctionName parameter could not be found') ||
        error.message.includes('Cannot find module')) {
        console.log('🔄 检测到云函数部署问题，自动切换到模拟模式')
        getApp().globalData.useMockData = true

        wx.showModal({
          title: '提示',
          content: '云函数正在部署中，当前使用模拟数据进行演示。部署完成后将自动切换到真实模式。',
          showCancel: false,
          confirmText: '知道了'
        })

        return {
          success: true,
          taskId: 'MOCK_TASK_' + Date.now(),
          totalSlices: 5,
          estimatedTime: 60,
          duration: 2.5
        }
      }

      throw new Error('创建解析任务失败')
    }
  },

  // 使用全局轮询管理器 - 修复高频轮询问题
  startPollingTaskStatus() {
    console.log('🔄 使用全局轮询管理器开始轮询任务状态')

    // 清除之前的定时器
    this.clearPollingTimer()

    if (!this.data.taskId) {
      console.error('❌ 没有任务ID，无法开始轮询');
      return;
    }

    // 引入轮询管理器
    const pollingManager = require('../../utils/polling-manager.js');

    // 进度回调
    const onProgress = (task) => {
      console.log(`📋 任务进度更新:`, task);
      this.setData({
        taskMessage: task.message || '任务处理中...',
        taskProgress: task.progress || 0
      });
    };

    // 完成回调
    const onComplete = (task) => {
      console.log('✅ 任务完成');
      this.handleTaskCompleted(task);
    };

    // 错误回调
    const onError = (error) => {
      console.error('❌ 轮询失败:', error);
      this.handleTaskFailed({ error: error.message });
    };

    // 启动轮询
    const started = pollingManager.startPolling(this.data.taskId, onProgress, onComplete, onError);

    if (!started) {
      console.log('⚠️ 任务已在轮询中，等待现有轮询完成...');
      this.setData({
        taskMessage: '任务处理中，请稍候...'
      });
    }
  },

  // 轮询任务状态 - 已废弃，使用全局轮询管理器
  pollTaskStatus() {
    console.log('⚠️ pollTaskStatus方法已废弃，请使用startPollingTaskStatus');
    // 为了兼容性保留，但不执行任何操作
  },

  // 获取任务状态消息
  getTaskStatusMessage(status, progress) {
    switch (status) {
      case 'queued':
        return '⏳ 任务排队中...';
      case 'processing':
        return `🔄 AI正在分析简历... ${progress}%`;
      case 'completed':
        return '✅ 分析完成！';
      case 'failed':
        return '❌ 分析失败';
      default:
        return '⏳ 处理中...';
    }
  },

  // 处理任务完成
  handleTaskCompleted(task) {
    console.log('🎉 任务完成处理:', task);

    this.setData({
      taskStatus: 'completed',
      progressPercent: 100,
      taskMessage: '✅ 简历分析完成！',
      bricks: task.bricks || [],
      uploadedBricks: task.bricks || [], // 同时设置uploadedBricks供按钮使用
      bricksCount: task.bricksCount || 0
    });

    // 🔧 修复：将积木数据通过积木管理器保存，确保云端同步
    const bricksData = task.bricks || [];
    if (bricksData.length > 0) {
      try {
        const app = getApp();

        // 使用积木管理器保存数据，确保云端同步
        if (app.brickManager) {
          console.log('📦 通过积木管理器保存积木数据...');

          // 批量添加积木到云端
          for (const brick of bricksData) {
            try {
              await app.brickManager.addBrick(brick);
              console.log(`✅ 积木 "${brick.title}" 已保存`);
            } catch (error) {
              console.warn(`⚠️ 积木 "${brick.title}" 保存失败:`, error);
            }
          }

          console.log('✅ 积木数据批量保存完成');
        } else {
          // 降级方案：保存到本地存储
          console.warn('⚠️ 积木管理器未初始化，使用本地存储降级方案');
          wx.setStorageSync('bricks', bricksData);
          app.globalData.bricks = bricksData;
          app.globalData.latestBricks = bricksData;
        }

        console.log('📦 积木数据保存完成:', {
          totalCount: bricksData.length,
          managerAvailable: !!app.brickManager
        });
      } catch (error) {
        console.error('❌ 保存积木数据失败:', error);

        // 最终降级方案：保存到本地存储
        try {
          wx.setStorageSync('bricks', bricksData);
          const app = getApp();
          app.globalData.bricks = bricksData;
          app.globalData.latestBricks = bricksData;
          console.log('✅ 已使用本地存储降级方案保存积木数据');
        } catch (fallbackError) {
          console.error('❌ 本地存储降级方案也失败:', fallbackError);
        }
      }
    }

    // 显示完成消息
    this.showCompletionMessage(task);

    // 显示JD分析选项，不自动跳转
    this.showJdAnalysisOption();
  },

  // 禁用自动跳转，改为手动跳转提示
  showJdAnalysisOption() {
    console.log('🎉 简历分析完成，显示JD分析选项');

    // 只显示完成提示，不自动跳转
    wx.showToast({
      title: '简历分析完成！',
      icon: 'success',
      duration: 2000
    });

    // 显示手动跳转按钮或提示
    this.setData({
      showJdAnalysisButton: true,
      analysisCompleted: true
    });

    console.log('✅ 用户可以手动选择是否进入JD分析页面');
  },

  // 🔥 新增：执行跳转到JD分析页面
  navigateToJdAnalysis() {
    console.log('🎯 执行跳转到JD分析页面');

    // 获取积木数据和用户信息
    const bricksData = this.data.uploadedBricks || this.data.bricks || [];
    const bricksCount = this.data.bricksCount || bricksData.length;
    const userName = this.data.userName || '用户';

    console.log('📦 准备传递数据:', {
      bricksCount,
      userName,
      bricksDataLength: bricksData.length
    });

    // 如果没有积木数据，提示用户
    if (bricksData.length === 0) {
      wx.showModal({
        title: '数据加载中',
        content: '积木数据正在加载中，请稍后再试或手动点击"立即填写JD"按钮',
        showCancel: false,
        confirmText: '知道了'
      });
      return;
    }

    // 显示跳转提示
    wx.showLoading({
      title: '正在跳转...',
      mask: true
    });

    // 延迟一下再跳转，给用户一个过渡体验
    setTimeout(() => {
      wx.hideLoading();

      // 构建跳转URL，包含必要的数据
      const jumpUrl = `/pages/jd-input/jd-input?bricksCount=${bricksCount}&userName=${encodeURIComponent(userName)}&autoJump=true`;

      wx.navigateTo({
        url: jumpUrl,
        success: () => {
          console.log('✅ 成功跳转到JD分析页面，传递参数:', { bricksCount, userName });

          // 📝 将积木数据存储到全局，供JD页面获取
          const app = getApp();
          app.globalData.latestBricks = bricksData;
          app.globalData.latestAnalysisResult = {
            bricks: bricksData,
            bricksCount: bricksCount,
            userName: userName
          };

          // 可以在这里发送埋点数据
          try {
            wx.reportAnalytics('auto_navigate_to_jd', {
              from_page: 'upload',
              completion_time: new Date().toISOString(),
              bricks_count: bricksCount
            });
          } catch (e) {
            console.log('📊 埋点上报失败，但不影响功能:', e);
          }
        },
        fail: (error) => {
          console.error('❌ 跳转失败:', error);
          wx.showToast({
            title: '页面跳转失败',
            icon: 'error'
          });
        }
      });
    }, 1000);
  },

  // 处理任务失败
  handleTaskFailed(task) {
    console.log('💥 任务失败处理:', task);

    this.setData({
      taskStatus: 'failed',
      taskMessage: '❌ 简历分析失败'
    });

    // 显示失败消息
    this.showFailureMessage(task);
  },

  // 切换到模拟模式
  switchToMockMode() {
    console.log('🔄 切换到模拟模式');
    getApp().globalData.useMockData = true;

    wx.showModal({
      title: '提示',
      content: '云函数正在部署中，当前使用模拟数据进行演示。',
      showCancel: false,
      confirmText: '知道了'
    });
  },

  // 清除轮询定时器
  clearPollingTimer() {
    if (this.data.pollingTimer) {
      clearInterval(this.data.pollingTimer);
      this.setData({
        pollingTimer: null
      });
      console.log('🛑 轮询定时器已清除');
    }
  },

  // 处理轮询超时
  handlePollingTimeout() {
    console.warn('⏰ 轮询超时，可能任务处理时间过长');

    this.clearPollingTimer();

    wx.showModal({
      title: '处理超时',
      content: 'AI分析时间较长，您可以选择继续等待或稍后查看结果',
      cancelText: '稍后查看',
      confirmText: '继续等待',
      success: (res) => {
        if (res.confirm) {
          // 继续等待，重新开始轮询
          this.startPollingTaskStatus();
        } else {
          // 稍后查看，跳转到其他页面或保持当前状态
          this.setData({
            taskMessage: '⏳ 任务仍在后台处理中，请稍后查看结果'
          });
        }
      }
    });
  },

  // 获取最新任务
  getLatestTask() {
    const db = wx.cloud.database();
    db.collection('resume_tasks')
      .orderBy('createdAt', 'desc')
      .limit(1)
      .get()
      .then(res => {
        if (res.data.length > 0) {
          const latestTask = res.data[0];
          this.setData({ taskId: latestTask.taskId });
          console.log('✅ 获取到最新任务ID:', latestTask.taskId);
          this.pollTaskStatus();
        } else {
          console.log('⚠️  数据库中没有任务记录');
        }
      })
      .catch(error => {
        console.error('获取最新任务失败:', error);
      });
  },

  // 显示处理选择对话框
  showProcessingChoice() {
    wx.showModal({
      title: '简历已上传',
      content: 'AI正在处理您的简历，预计需要3-5分钟。您可以选择继续等待或稍后查看结果。',
      cancelText: '稍后查看',
      confirmText: '继续等待',
      success: (res) => {
        if (res.cancel) {
          // 用户选择稍后查看
          this.setData({
            taskMessage: '⏳ 简历分析中，您可以稍后回来查看结果',
            analysisStatus: 'analyzing'
          })
        } else {
          // 用户选择继续等待，保持当前状态
          this.setData({
            taskMessage: '⏳ 请耐心等待，AI正在为您分析简历...'
          })
        }
      }
    })
  },

  // 显示完成消息
  showCompletionMessage(data) {
    wx.showModal({
      title: '分析完成！',
      content: `恭喜！我们为您生成了 ${data.bricksCount} 个能力积木。现在您可以去填写岗位JD，获取匹配的能力积木组合。`,
      cancelText: '查看积木',
      confirmText: '填写JD',
      success: (res) => {
        if (res.confirm) {
          this.goToJdInput()
        } else {
          this.showBricksResult()
        }
      }
    })
  },

  // 显示失败消息
  showFailureMessage(data) {
    wx.showModal({
      title: '分析失败',
      content: data.error || '简历分析失败，建议重新上传或检查文件格式。',
      cancelText: '稍后重试',
      confirmText: '立即重试',
      success: (res) => {
        if (res.confirm) {
          this.resetUploadState()
        }
      }
    })
  },

  // 重置上传状态
  resetUploadState() {
    this.clearPollingTimer()

    this.setData({
      selectedFile: null,
      fileName: '',
      fileSize: 0,
      fileSizeText: '',
      isUploading: false,
      currentStep: 1,
      progressPercent: 0,
      taskId: null,
      taskStatus: 'idle',
      taskProgress: 0,
      taskMessage: '',
      pollingCount: 0,
      analysisStatus: 'idle',
      bricks: [],
      uploadedBricks: [],
      bricksCount: 0
    })
  },

  // 显示积木结果 - 跳转到积木库页面
  showBricksResult() {
    // 检查是否有积木数据
    const bricksData = this.data.uploadedBricks || this.data.bricks || []

    if (bricksData.length === 0) {
      wx.showToast({
        title: '暂无积木数据',
        icon: 'error'
      })
      return
    }

    console.log('📋 跳转到积木库页面，积木数量:', bricksData.length)

    // 跳转到积木库页面
    wx.switchTab({
      url: '/pages/bricks/bricks',
      success: () => {
        wx.showToast({
          title: `共${bricksData.length}个积木`,
          icon: 'success'
        })
      },
      fail: (error) => {
        console.error('❌ 跳转积木库失败:', error)
        wx.showToast({
          title: '跳转失败',
          icon: 'error'
        })
      }
    })
  },

  // 跳转到JD输入页面
  goToJdInput() {
    // 检查是否有积木数据
    const bricksData = this.data.uploadedBricks || this.data.bricks || []

    if (bricksData.length === 0) {
      wx.showToast({
        title: '请先完成简历分析',
        icon: 'error'
      })
      return
    }

    const userName = this.data.userName || '用户'

    console.log('🎯 跳转到JD输入页面，积木数量:', bricksData.length)

    // 📝 将积木数据存储到全局，供JD页面获取
    const app = getApp()
    app.globalData.latestBricks = bricksData
    app.globalData.latestAnalysisResult = {
      bricks: bricksData,
      bricksCount: bricksData.length,
      userName: userName
    }

    // 携带积木数据跳转
    wx.navigateTo({
      url: `/pages/jd-input/jd-input?bricksCount=${bricksData.length}&userName=${encodeURIComponent(userName)}&manualJump=true`,
      success: () => {
        console.log('✅ 成功跳转到JD输入页面')
      },
      fail: (error) => {
        console.error('❌ 跳转JD输入页面失败:', error)
        wx.showToast({
          title: '跳转失败',
          icon: 'error'
        })
      }
    })
  },

  // 重新上传
  reUpload() {
    this.resetUploadState()
    this.chooseFile()
  },

  // 手动查询任务状态（用户主动查询）
  async manualCheckTaskStatus() {
    if (!this.data.taskId) {
      wx.showToast({
        title: '没有进行中的任务',
        icon: 'error'
      })
      return
    }

    wx.showLoading({
      title: '查询状态中...'
    })

    try {
      const response = await wx.cloud.callFunction({
        name: 'get-task-result',
        data: {
          taskId: this.data.taskId
        }
      })

      wx.hideLoading()

      if (response.result.success) {
        await this.handleTaskStatusUpdate(response.result)

        wx.showToast({
          title: '状态已更新',
          icon: 'success'
        })
      } else {
        wx.showToast({
          title: response.result.message || '查询失败',
          icon: 'error'
        })
      }

    } catch (error) {
      wx.hideLoading()
      console.error('❌ 手动查询任务状态失败:', error)
      wx.showToast({
        title: '查询失败，请重试',
        icon: 'error'
      })
    }
  },

  // 格式化文件大小
  formatFileSize(size) {
    if (!size || size === 0) {
      return ''
    }
    if (size < 1024) {
      return `${size} B`
    } else if (size < 1024 * 1024) {
      return `${(size / 1024).toFixed(2)} KB`
    } else if (size < 1024 * 1024 * 1024) {
      return `${(size / (1024 * 1024)).toFixed(2)} MB`
    } else {
      return `${(size / (1024 * 1024 * 1024)).toFixed(2)} GB`
    }
  }
})